import { celebrate, Joi, Segments } from "celebrate";
import {
  TICKET_MODULE,
  TICKET_TYPE,
  TICKET_PRIORITY,
  TICKET_STATUS,
  VALIDATION_CONSTANT,
} from "../helper/constant";

const createTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        ticket_title: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_TITLE_MIN_LENGTH)
          .max(VALIDATION_CONSTANT.TICKET_TITLE_MAX_LENGTH)
          .required(),
        ticket_description: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MIN_LENGTH)
          .max(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MAX_LENGTH)
          .required(),
        ticket_module: Joi.string()
          .valid(...Object.values(TICKET_MODULE))
          .default(TICKET_MODULE.OTHER),
        ticket_type: Joi.string()
          .valid(...Object.values(TICKET_TYPE))
          .default(TICKET_TYPE.GENERAL_QUERY),
        ticket_priority: Joi.string()
          .valid(...Object.values(TICKET_PRIORITY))
          .default(TICKET_PRIORITY.MEDIUM),
        ticket_owner_name: Joi.string().min(2).max(100).required(),
        ticket_owner_email: Joi.string().email().required(),
        ticket_owner_phone: Joi.string().min(10).max(20).optional(),
        // File uploads handled by multer
        attachments: Joi.any().optional(),
      })
      .unknown(true),
  });

const updateTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        ticket_title: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_TITLE_MIN_LENGTH)
          .max(VALIDATION_CONSTANT.TICKET_TITLE_MAX_LENGTH)
          .optional(),
        ticket_description: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MIN_LENGTH)
          .max(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MAX_LENGTH)
          .optional(),
        ticket_module: Joi.string()
          .valid(...Object.values(TICKET_MODULE))
          .optional(),
        ticket_type: Joi.string()
          .valid(...Object.values(TICKET_TYPE))
          .optional(),
        ticket_priority: Joi.string()
          .valid(...Object.values(TICKET_PRIORITY))
          .optional(),
        ticket_status: Joi.string()
          .valid(...Object.values(TICKET_STATUS))
          .optional(),
        assigned_to_user_id: Joi.number().optional(),
        resolution_note: Joi.string()
          .max(VALIDATION_CONSTANT.RESOLUTION_NOTE_MAX_LENGTH)
          .optional(),
        rating: Joi.number()
          .min(VALIDATION_CONSTANT.RATING_MIN)
          .max(VALIDATION_CONSTANT.RATING_MAX)
          .optional(),
        review_comment: Joi.string()
          .max(VALIDATION_CONSTANT.REVIEW_COMMENT_MAX_LENGTH)
          .optional(),
      })
      .unknown(true),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getTicketValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const deleteTicketValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getTicketsListValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      organization_id: Joi.string().optional(),
      ticket_status: Joi.string()
        .valid(...Object.values(TICKET_STATUS))
        .optional(),
      ticket_priority: Joi.string()
        .valid(...Object.values(TICKET_PRIORITY))
        .optional(),
      ticket_module: Joi.string()
        .valid(...Object.values(TICKET_MODULE))
        .optional(),
      ticket_type: Joi.string()
        .valid(...Object.values(TICKET_TYPE))
        .optional(),
      assigned_to_user_id: Joi.number().optional(),
      search: Joi.string().optional(),
      sort_by: Joi.string()
        .valid("created_at", "updated_at", "ticket_priority", "ticket_status")
        .optional(),
      sort_order: Joi.string().valid("ASC", "DESC").optional(),
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
    }),
  });

const assignTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      assigned_to_user_id: Joi.number().required(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const resolveTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      resolution_note: Joi.string()
        .min(10)
        .max(VALIDATION_CONSTANT.RESOLUTION_NOTE_MAX_LENGTH)
        .required(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const rateTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      rating: Joi.number()
        .min(VALIDATION_CONSTANT.RATING_MIN)
        .max(VALIDATION_CONSTANT.RATING_MAX)
        .required(),
      review_comment: Joi.string()
        .max(VALIDATION_CONSTANT.REVIEW_COMMENT_MAX_LENGTH)
        .optional(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

export default {
  createTicketValidator,
  updateTicketValidator,
  getTicketValidator,
  deleteTicketValidator,
  getTicketsListValidator,
  assignTicketValidator,
  resolveTicketValidator,
  rateTicketValidator,
};
