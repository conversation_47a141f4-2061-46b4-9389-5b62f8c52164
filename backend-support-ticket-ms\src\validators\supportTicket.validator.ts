import { celebrate, Joi, Segments } from "celebrate";
import {
  ModuleType,
  IssueType,
  Priority,
  TicketStatus,
} from "../models/Ticket";

const createTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        ticket_title: Joi.string().min(5).max(200).required(),
        ticket_description: Joi.string().min(10).max(5000).required(),
        ticket_module: Joi.string()
          .valid(...Object.values(ModuleType))
          .default(ModuleType.OTHER),
        ticket_type: Joi.string()
          .valid(...Object.values(IssueType))
          .default(IssueType.GENERAL_QUERY),
        ticket_priority: Joi.string()
          .valid(...Object.values(Priority))
          .default(Priority.MEDIUM),
        ticket_owner_name: Joi.string().min(2).max(100).required(),
        ticket_owner_email: Joi.string().email().required(),
        ticket_owner_phone: Joi.string().min(10).max(20).optional(),
        // File uploads handled by multer
        attachments: Joi.any().optional(),
      })
      .unknown(true),
  });

const updateTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        ticket_title: Joi.string().min(5).max(200).optional(),
        ticket_description: Joi.string().min(10).max(5000).optional(),
        ticket_module: Joi.string()
          .valid(...Object.values(ModuleType))
          .optional(),
        ticket_type: Joi.string()
          .valid(...Object.values(IssueType))
          .optional(),
        ticket_priority: Joi.string()
          .valid(...Object.values(Priority))
          .optional(),
        ticket_status: Joi.string()
          .valid(...Object.values(TicketStatus))
          .optional(),
        assigned_to_user_id: Joi.number().optional(),
        resolution_note: Joi.string().max(2000).optional(),
        rating: Joi.number().min(1).max(5).optional(),
        review_comment: Joi.string().max(1000).optional(),
      })
      .unknown(true),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getTicketValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const deleteTicketValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getTicketsListValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      organization_id: Joi.string().optional(),
      ticket_status: Joi.string()
        .valid(...Object.values(TicketStatus))
        .optional(),
      ticket_priority: Joi.string()
        .valid(...Object.values(Priority))
        .optional(),
      ticket_module: Joi.string()
        .valid(...Object.values(ModuleType))
        .optional(),
      ticket_type: Joi.string()
        .valid(...Object.values(IssueType))
        .optional(),
      assigned_to_user_id: Joi.number().optional(),
      search: Joi.string().optional(),
      sort_by: Joi.string()
        .valid("created_at", "updated_at", "ticket_priority", "ticket_status")
        .optional(),
      sort_order: Joi.string().valid("ASC", "DESC").optional(),
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
    }),
  });

const assignTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      assigned_to_user_id: Joi.number().required(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const resolveTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      resolution_note: Joi.string().min(10).max(2000).required(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const rateTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      rating: Joi.number().min(1).max(5).required(),
      review_comment: Joi.string().max(1000).optional(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

export default {
  createTicketValidator,
  updateTicketValidator,
  getTicketValidator,
  deleteTicketValidator,
  getTicketsListValidator,
  assignTicketValidator,
  resolveTicketValidator,
  rateTicketValidator,
};
