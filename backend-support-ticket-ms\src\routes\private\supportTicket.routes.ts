import { Router } from "express";
import { celebrate, Joi, Segments } from "celebrate";
// Import individual controller functions following recipe-ms pattern
import {
  createTicket,
  getAllTickets,
  getTicketById,
  updateTicket,
  deleteTicket,
  assignTicket,
  resolveTicket,
  rateTicket,
} from "../../controller/supportTicket.controller";
import uploadService from "../../helper/upload.service";
import { SUPPORT_FILE_UPLOAD_CONSTANT } from "../../utils/common";
import {
  createTicketValidator,
  getTicketsValidator,
  getTicketByIdValidator,
  updateTicketValidator,
  assignTicketValidator,
  resolveTicketValidator,
  rateTicketValidator,
} from "../../validators/supportTicket.validator";

// Configure S3 Upload for Support Ticket Files (Following Recipe Pattern)
const multerS3Upload = uploadService.multerS3(
  process.env.NODE_ENV || "development",
  SUPPORT_FILE_UPLOAD_CONSTANT.TICKET_ATTACHMENT.folder
);

const router = Router();

// GET /tickets - Get all tickets with pagination and filters
router.get("/tickets", getTicketsValidator(), getAllTickets);

// POST /tickets - Create new ticket with file upload
router.post(
  "/tickets",
  multerS3Upload.fields([{ name: "ticketFiles", maxCount: 5 }]),
  createTicketValidator(),
  createTicket
);

// GET /tickets/:id - Get ticket by ID
router.get("/tickets/:id", getTicketByIdValidator(), getTicketById);

// PUT /tickets/:id/assign - Assign ticket to user
router.put("/tickets/:id/assign", assignTicketValidator(), assignTicket);

// PUT /tickets/:id/resolve - Resolve ticket
router.put("/tickets/:id/resolve", resolveTicketValidator(), resolveTicket);

// PUT /tickets/:id - Update ticket with file upload
router.put(
  "/tickets/:id",
  multerS3Upload.fields([{ name: "ticketFiles", maxCount: 5 }]),
  updateTicketValidator(),
  updateTicket
);

// DELETE /tickets/:id - Delete ticket
router.delete("/tickets/:id", getTicketByIdValidator(), deleteTicket);

// POST /tickets/:id/rate - Rate ticket
router.post("/tickets/:id/rate", rateTicketValidator(), rateTicket);

// Export routes following recipe-ms pattern
export default router;
