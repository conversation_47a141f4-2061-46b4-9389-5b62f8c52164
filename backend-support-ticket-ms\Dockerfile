FROM node:20 AS build_image

RUN apt-get update && apt-get install -y 

# Set the working directory
WORKDIR /usr/src/backend-support-ticket-ms

# Copy only package.json and package-lock.json to install dependencies
COPY package*.json ./

# Install dependencies
RUN yarn install 

# Copy the rest of the application files
COPY . .

# Build the Node.js app
RUN yarn build

# remove dev dependencies
RUN npm prune --production

# Stage 2: Production Image
FROM node:20

# Set the working directory
WORKDIR /usr/src/backend-support-ticket-ms

# Install production dependencies only
COPY --from=build_image /usr/src/backend-support-ticket-ms/package.json ./package.json
COPY --from=build_image /usr/src/backend-support-ticket-ms/node_modules ./node_modules
COPY --from=build_image /usr/src/backend-support-ticket-ms/build ./build

# Expose the port that the Node.js app runs on
EXPOSE 9030

# Start the Node.js app
CMD ["node", "./build/src/index.js"]