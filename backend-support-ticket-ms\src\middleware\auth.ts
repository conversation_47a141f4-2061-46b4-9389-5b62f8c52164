import { StatusCodes } from "http-status-codes";
import _ from "lodash";
import Jwt from "jsonwebtoken";
import { getUser, getUserAllRoles, getUserSession } from "../utils/common";
import { NORMAL_USER, ADMIN_SIDE_USER } from "../utils/constant";

const userAuth = async (req: any, res: any, next: any) => {
  try {
    /** check Token is present  */
    if (!req.headers.authorization) {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .send({ status: false, message: res.__("ERROR_TOKEN_REQUIRED") });
    }
    const token: any = req.headers?.authorization?.split(" ")[1];

    if (req.headers.authorization) {
      const decoded = Jwt.decode(token);

      // Check if token is valid and decoded properly
      if (!decoded || typeof decoded !== "object" || !decoded.user_id) {
        return res
          .status(StatusCodes.UNAUTHORIZED)
          .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
      }

      req.user = await getUser(decoded.user_id, true);

      if (!req?.user?.id) {
        return res
          .status(StatusCodes.UNAUTHORIZED)
          .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
      }

      const userRoles = await getUserAllRoles(req?.user?.id);
      const loginUserRoles =
        userRoles.length > 0
          ? _.map(userRoles, (userRole: any) => userRole.role_name)
          : [];

      const normalUser = [...NORMAL_USER];
      const adminSideUser = [...ADMIN_SIDE_USER];

      if (
        !loginUserRoles.some((item: any) => adminSideUser.includes(item)) &&
        req.headers["platform-type"] == "web"
      ) {
        return res
          .status(StatusCodes.UNAUTHORIZED)
          .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
      }

      if (
        !loginUserRoles.some((item: any) => normalUser.includes(item)) &&
        (req.headers["platform-type"] == "ios" ||
          req.headers["platform-type"] == "android")
      ) {
        return res
          .status(StatusCodes.UNAUTHORIZED)
          .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
      }
      if (userRoles.length > 0) {
        const roleData: any = userRoles[0];

        if (process.env.NEXT_NODE_ENV !== "staging" && roleData.id !== 1) {
          let deviceType = req.headers["platform-type"];
          deviceType = deviceType == "ios" ? "android" : deviceType;
          const session = await getUserSession(token, deviceType);

          if (!session) {
            return res
              .status(StatusCodes.UNAUTHORIZED)
              .json({ status: false, message: res.__("ERROR_INVALID_TOKEN") });
          }
        }
      }
      req.user.roles = userRoles;
      next();
    }
  } catch (e: any) {
    console.error("Authentication error:", e);
    return res.status(StatusCodes.UNAUTHORIZED).json({
      status: false,
      message: res.__("ERROR_INVALID_TOKEN"),
    });
  }
};

/**
 * Optional authentication middleware
 * Sets user information if token is provided, but doesn't require it
 */
export const optionalAuth = async (req: any, res: any, next: any) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      // No token provided, continue without user
      return next();
    }

    const token = authHeader.substring(7);

    if (!token) {
      return next();
    }

    try {
      const decoded = Jwt.decode(token);

      if (decoded && typeof decoded === "object" && decoded.user_id) {
        const user = await getUser(decoded.user_id, true);

        if (user) {
          const userRoles = await getUserAllRoles(user.id);
          req.user = user;
          req.user.roles = userRoles;
        }
      }
    } catch (jwtError) {
      // Invalid token, but continue without user
      console.warn(
        "Invalid token in optional auth:",
        jwtError instanceof Error ? jwtError.message : "Unknown error"
      );
    }

    next();
  } catch (error) {
    console.error("Optional authentication error:", error);
    next(); // Continue even if there's an error
  }
};

export default userAuth;
