import { Router } from "express";
import supportTicketRoutes from "./supportTicket.routes";
import supportMessageRoutes from "./supportMessage.routes";
import supportConfigRoutes from "./supportConfig.routes";
import adminRoutes from "./admin.routes";

const router = Router();

// Enhanced support system routes (TTH architecture)
// User management features are integrated into these core routes
router.use("/support", supportTicketRoutes);
router.use("/support", supportMessageRoutes);
router.use("/support", supportConfigRoutes);

// Admin routes (TTH architecture)
router.use("/admin", adminRoutes);

// Legacy dashboard redirect
router.get("/dashboard", async (_req, res) => {
  res.redirect("/api/private/admin/dashboard");
});

export default router;
