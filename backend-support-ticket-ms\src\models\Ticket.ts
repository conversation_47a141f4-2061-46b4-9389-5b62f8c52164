import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import {
  TICKET_MODULE,
  TICKET_TYPE,
  TICKET_PRIORITY,
  TICKET_STATUS,
} from "../helper/constant";

interface TicketAttributes {
  id?: number;
  ticket_number?: string;
  organization_id: string;

  // User identification - store only user_id, fetch details via queries
  ticket_owner_user_id?: number;
  ticket_owner_name: string;
  ticket_owner_email: string;
  ticket_owner_phone?: string;

  ticket_title: string;
  ticket_description: string;
  ticket_module: keyof typeof TICKET_MODULE;
  ticket_type: keyof typeof TICKET_TYPE;
  ticket_priority: keyof typeof TICKET_PRIORITY;
  ticket_status: keyof typeof TICKET_STATUS;

  // Assignment tracking
  assigned_to_user_id?: number;
  assigned_at?: Date;
  assigned_by?: number;

  // Resolution tracking
  resolution_note?: string;
  resolved_at?: Date;
  resolved_by?: number;

  // Rating and review
  rating?: number;
  review_comment?: string;
  reviewed_at?: Date;

  // SLA tracking
  sla_due_date?: Date;
  first_response_at?: Date;

  // Audit fields
  created_by: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
}

export default class Ticket
  extends Model<TicketAttributes>
  implements TicketAttributes
{
  public id!: number;
  public ticket_number!: string;
  public organization_id!: string;

  // User identification
  public ticket_owner_user_id?: number;
  public ticket_owner_name!: string;
  public ticket_owner_email!: string;
  public ticket_owner_phone?: string;

  public ticket_title!: string;
  public ticket_description!: string;
  public ticket_module!: keyof typeof TICKET_MODULE;
  public ticket_type!: keyof typeof TICKET_TYPE;
  public ticket_priority!: keyof typeof TICKET_PRIORITY;
  public ticket_status!: keyof typeof TICKET_STATUS;

  // Assignment tracking
  public assigned_to_user_id?: number;
  public assigned_at?: Date;
  public assigned_by?: number;

  // Resolution tracking
  public resolution_note?: string;
  public resolved_at?: Date;
  public resolved_by?: number;

  // Rating and review
  public rating?: number;
  public review_comment?: string;
  public reviewed_at?: Date;

  // SLA tracking
  public sla_due_date?: Date;
  public first_response_at?: Date;

  // Audit fields
  public created_by!: number;
  public updated_by?: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
  public readonly deleted_at?: Date;

  static associate(models: any) {
    // Ticket has many attachments
    Ticket.hasMany(models.TicketAttachment, {
      foreignKey: "ticket_id",
      as: "attachments",
    });
  }
}

Ticket.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    ticket_number: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
    },
    organization_id: {
      type: DataTypes.STRING(50),
      allowNull: false,
    },
    ticket_owner_user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ticket_owner_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    ticket_owner_email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        isEmail: true,
      },
    },
    ticket_owner_phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
    },
    ticket_title: {
      type: DataTypes.STRING(200),
      allowNull: false,
    },
    ticket_description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ticket_module: {
      type: DataTypes.ENUM(...Object.values(TICKET_MODULE)),
      allowNull: false,
      defaultValue: TICKET_MODULE.OTHER,
    },
    ticket_type: {
      type: DataTypes.ENUM(...Object.values(TICKET_TYPE)),
      allowNull: false,
      defaultValue: TICKET_TYPE.GENERAL_QUERY,
    },
    ticket_priority: {
      type: DataTypes.ENUM(...Object.values(TICKET_PRIORITY)),
      allowNull: false,
      defaultValue: TICKET_PRIORITY.MEDIUM,
    },
    ticket_status: {
      type: DataTypes.ENUM(...Object.values(TICKET_STATUS)),
      allowNull: false,
      defaultValue: TICKET_STATUS.OPEN,
    },
    assigned_to_user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    assigned_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    assigned_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    resolution_note: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    resolved_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    resolved_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5,
      },
    },
    review_comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    reviewed_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    sla_due_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    first_response_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
  },
  {
    sequelize,
    tableName: "mo_support_tickets",
    modelName: "Ticket",
    timestamps: true,
    paranoid: true,
    underscored: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    deletedAt: "deleted_at",
    indexes: [
      {
        fields: ["organization_id"],
      },
      {
        fields: ["ticket_status"],
      },
      {
        fields: ["ticket_priority"],
      },
      {
        fields: ["assigned_to_user_id"],
      },
      {
        fields: ["created_at"],
      },
    ],
  }
);
