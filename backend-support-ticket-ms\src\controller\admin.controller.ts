import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { db } from "../models";
import { TicketStatus } from "../models/Ticket";
import { getEffectiveOrganizationId } from "../middleware/adminAuth";
import { getPagination, getPaginatedItems } from "../utils/common";

// Get models from db object to ensure associations are set up (following recipe pattern)
const Ticket = db.Ticket;
const TicketMessage = db.TicketMessage;
const TicketHistory = db.TicketHistory;

/**
 * @description Admin Dashboard - Get overview statistics
 * @route GET /api/v1/private/admin/dashboard
 * @access Private (Admin only)
 */
const getDashboard = async (req: Request, res: Response): Promise<any> => {
  try {
    const user = (req as any).user;
    const { organization_id } = req.query;

    const effectiveOrgId = await getEffectiveOrganizationId(
      user,
      organization_id as string
    );

    // Build where clause for organization filtering
    const whereClause: any = {};
    if (effectiveOrgId !== undefined) {
      whereClause.organization_id = effectiveOrgId;
    }

    // Get ticket statistics
    const totalTickets = await Ticket.count({ where: whereClause });

    const ticketsByStatus = await Ticket.findAll({
      where: whereClause,
      attributes: [
        "ticket_status",
        [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "count"],
      ],
      group: ["ticket_status"],
      raw: true,
    });

    const ticketsByPriority = await Ticket.findAll({
      where: whereClause,
      attributes: [
        "priority",
        [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "count"],
      ],
      group: ["priority"],
      raw: true,
    });

    const ticketsByModule = await Ticket.findAll({
      where: whereClause,
      attributes: [
        "module_type",
        [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "count"],
      ],
      group: ["module_type"],
      raw: true,
    });

    // Get SLA metrics
    const overdueTickets = await Ticket.count({
      where: {
        ...whereClause,
        sla_due_date: { [Op.lt]: new Date() },
        ticket_status: {
          [Op.notIn]: [TicketStatus.RESOLVED, TicketStatus.CLOSED],
        },
      },
    });

    // Get recent activity
    const recentTickets = await Ticket.findAll({
      where: whereClause,
      order: [["created_at", "DESC"]],
      limit: 5,
      attributes: [
        "id",
        "ticket_number",
        "subject",
        "ticket_status",
        "priority",
        "created_at",
      ],
    });

    // Get unassigned tickets
    const unassignedTickets = await Ticket.count({
      where: {
        ...whereClause,
        assigned_to_user_id: null,
        ticket_status: {
          [Op.notIn]: [TicketStatus.RESOLVED, TicketStatus.CLOSED],
        },
      },
    });

    // Get average response time (in hours)
    const avgResponseTime: any = await Ticket.findOne({
      where: {
        ...whereClause,
        first_response_at: { [Op.not]: null },
      },
      attributes: [
        [
          Ticket.sequelize!.fn(
            "AVG",
            Ticket.sequelize!.literal(
              "TIMESTAMPDIFF(HOUR, created_at, first_response_at)"
            )
          ),
          "avg_hours",
        ],
      ],
      raw: true,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("DASHBOARD_FETCHED_SUCCESSFULLY"),
      data: {
        overview: {
          total_tickets: totalTickets,
          unassigned_tickets: unassignedTickets,
          overdue_tickets: overdueTickets,
          avg_response_time_hours: avgResponseTime?.avg_hours || 0,
        },
        statistics: {
          by_status: ticketsByStatus,
          by_priority: ticketsByPriority,
          by_module: ticketsByModule,
        },
        recent_activity: recentTickets,
      },
    });
  } catch (error) {
    console.error("Error fetching admin dashboard:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("DASHBOARD_FETCH_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Admin Ticket Management with Advanced Filters
 * @route GET /api/v1/private/admin/tickets
 * @access Private (Admin only)
 */
const getTicketsWithFilters = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const user = (req as any).user;
    const {
      page,
      limit,
      search,
      status,
      priority,
      module_type,
      issue_type,
      assigned_to_user_id,
      organization_id,
      submitter_email,
      date_from,
      date_to,
      overdue,
      unassigned,
      has_rating,
      sort_by,
      sort_order,
    } = req.query;

    // Get pagination parameters using utility function
    const pagination = getPagination(page as string, limit as string);
    const isPaginated = !!(page || limit);

    const effectiveOrgId = await getEffectiveOrganizationId(
      user,
      organization_id as string
    );

    // Build where clause
    const whereClause: any = {};

    if (effectiveOrgId !== undefined) {
      whereClause.organization_id = effectiveOrgId;
    }

    // Search functionality
    if (search) {
      whereClause[Op.or] = [
        { ticket_number: { [Op.like]: `%${search}%` } },
        { subject: { [Op.like]: `%${search}%` } },
        { submitter_name: { [Op.like]: `%${search}%` } },
        { submitter_email: { [Op.like]: `%${search}%` } },
      ];
    }

    // Status filter
    if (status) {
      if (Array.isArray(status)) {
        whereClause.ticket_status = { [Op.in]: status };
      } else {
        whereClause.ticket_status = status;
      }
    }

    // Priority filter
    if (priority) {
      if (Array.isArray(priority)) {
        whereClause.priority = { [Op.in]: priority };
      } else {
        whereClause.priority = priority;
      }
    }

    // Module type filter
    if (module_type) {
      whereClause.module_type = module_type;
    }

    // Issue type filter
    if (issue_type) {
      whereClause.issue_type = issue_type;
    }

    // Assignment filter
    if (assigned_to_user_id === "null" || unassigned === "true") {
      whereClause.assigned_to_user_id = null;
    } else if (assigned_to_user_id) {
      whereClause.assigned_to_user_id = assigned_to_user_id;
    }

    // Submitter email filter
    if (submitter_email) {
      whereClause.submitter_email = { [Op.like]: `%${submitter_email}%` };
    }

    // Date range filter
    if (date_from || date_to) {
      whereClause.created_at = {};
      if (date_from) {
        whereClause.created_at[Op.gte] = new Date(date_from as string);
      }
      if (date_to) {
        whereClause.created_at[Op.lte] = new Date(date_to as string);
      }
    }

    // Overdue filter
    if (overdue === "true") {
      whereClause.sla_due_date = { [Op.lt]: new Date() };
      whereClause.ticket_status = {
        [Op.notIn]: [TicketStatus.RESOLVED, TicketStatus.CLOSED],
      };
    }

    // Has rating filter
    if (has_rating === "true") {
      whereClause.rating = { [Op.not]: null };
    } else if (has_rating === "false") {
      whereClause.rating = null;
    }

    // Build query options
    const queryOptions: any = {
      where: whereClause,
      include: [
        {
          model: TicketMessage,
          as: "messages",
          limit: 1,
          order: [["created_at", "DESC"]],
          required: false,
        },
      ],
      order: [
        [(sort_by as string) || "created_at", (sort_order as string) || "DESC"],
      ],
      distinct: true,
    };

    // Only add pagination if parameters provided
    if (isPaginated && pagination.limit) {
      queryOptions.limit = pagination.limit;
      queryOptions.offset = pagination.offset;
    }

    const { rows: tickets, count } = await Ticket.findAndCountAll(queryOptions);

    // Format response using utility function
    let responseData: any;
    if (isPaginated) {
      responseData = getPaginatedItems(
        { count, rows: tickets },
        page as string,
        pagination.limit!
      );
      // Add filters_applied to the response
      responseData.filters_applied = {
        search: search || null,
        status: status || null,
        priority: priority || null,
        module_type: module_type || null,
        organization_id: effectiveOrgId || null,
        overdue: overdue === "true",
        unassigned: unassigned === "true" || assigned_to_user_id === "null",
      };
    } else {
      responseData = {
        tickets,
        totalCount: count, // ✅ CONSISTENT: Use totalCount like other controllers
        filters_applied: {
          search: search || null,
          status: status || null,
          priority: priority || null,
          module_type: module_type || null,
          organization_id: effectiveOrgId || null,
          overdue: overdue === "true",
          unassigned: unassigned === "true" || assigned_to_user_id === "null",
        },
      };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("TICKETS_FETCHED_SUCCESSFULLY"),
      data: responseData,
    });
  } catch (error) {
    console.error("Error fetching tickets with filters:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("TICKETS_FETCH_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Bulk ticket operations
 * @route POST /api/v1/private/admin/tickets/bulk
 * @access Private (Admin only)
 */
const bulkTicketOperation = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { ticket_ids, operation, data } = req.body;
    const user = (req as any).user;

    if (!ticket_ids || !Array.isArray(ticket_ids) || ticket_ids.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("TICKET_IDS_REQUIRED"),
      });
    }

    const results = [];

    for (const ticketId of ticket_ids) {
      try {
        const ticket = await Ticket.findByPk(ticketId);
        if (!ticket) {
          results.push({
            ticket_id: ticketId,
            success: false,
            message: res.__("TICKET_NOT_FOUND"),
          });
          continue;
        }

        const previousStatus = ticket.ticket_status;
        let updateData: any = { updated_by: user.id };
        let historyNote = "";

        switch (operation) {
          case "assign":
            updateData.assigned_to_user_id = data.assigned_to_user_id;
            updateData.assigned_by = user.id;
            updateData.assigned_at = new Date();
            updateData.ticket_status = TicketStatus.ASSIGNED;
            historyNote = `Bulk assigned to user ${data.assigned_to_user_id}`;
            break;

          case "status_update":
            updateData.ticket_status = data.status;
            if (data.status === TicketStatus.RESOLVED) {
              updateData.resolved_at = new Date();
              updateData.resolved_by = user.id;
            }
            historyNote = `Bulk status update to ${data.status}`;
            break;

          case "priority_update":
            updateData.priority = data.priority;
            historyNote = `Bulk priority update to ${data.priority}`;
            break;

          case "delete":
            await ticket.destroy();
            results.push({
              ticket_id: ticketId,
              success: true,
              message: res.__("TICKET_DELETED"),
            });
            continue;

          default:
            results.push({
              ticket_id: ticketId,
              success: false,
              message: res.__("INVALID_OPERATION"),
            });
            continue;
        }

        await ticket.update(updateData);

        // Create history record
        await TicketHistory.create({
          ticket_id: ticketId,
          action_type: "BULK_OPERATION",
          previous_status: previousStatus,
          new_status: updateData.ticket_status || previousStatus,
          change_note: data.change_note || historyNote,
          created_by: user.id,
        });

        results.push({
          ticket_id: ticketId,
          success: true,
          message: res.__("OPERATION_COMPLETED"),
        });
      } catch (error) {
        results.push({
          ticket_id: ticketId,
          success: false,
          message: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("BULK_OPERATION_COMPLETED"),
      data: {
        operation,
        results,
        summary: {
          total: ticket_ids.length,
          successful: results.filter((r) => r.success).length,
          failed: results.filter((r) => !r.success).length,
        },
      },
    });
  } catch (error) {
    console.error("Error in bulk ticket operation:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("BULK_OPERATION_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Get ticket analytics and reports
 * @route GET /api/v1/private/admin/analytics
 * @access Private (Admin only)
 */
const getAnalytics = async (req: Request, res: Response): Promise<any> => {
  try {
    const user = (req as any).user;
    const {
      organization_id,
      date_from,
      date_to,
      period = "30d", // 7d, 30d, 90d, 1y
    } = req.query;

    const effectiveOrgId = await getEffectiveOrganizationId(
      user,
      organization_id as string
    );

    // Build base where clause
    const whereClause: any = {};
    if (effectiveOrgId !== undefined) {
      whereClause.organization_id = effectiveOrgId;
    }

    // Date range filtering
    let dateFilter: any = {};
    if (date_from || date_to) {
      if (date_from) {
        dateFilter[Op.gte] = new Date(date_from as string);
      }
      if (date_to) {
        dateFilter[Op.lte] = new Date(date_to as string);
      }
    } else {
      // Default period filtering
      const now = new Date();
      let daysBack = 30;

      switch (period) {
        case "7d":
          daysBack = 7;
          break;
        case "30d":
          daysBack = 30;
          break;
        case "90d":
          daysBack = 90;
          break;
        case "1y":
          daysBack = 365;
          break;
      }

      const startDate = new Date(
        now.getTime() - daysBack * 24 * 60 * 60 * 1000
      );
      dateFilter[Op.gte] = startDate;
    }

    if (Object.keys(dateFilter).length > 0) {
      whereClause.created_at = dateFilter;
    }

    // 1. Ticket Volume Analytics
    const totalTickets = await Ticket.count({ where: whereClause });

    const ticketTrends = await Ticket.findAll({
      where: whereClause,
      attributes: [
        [
          Ticket.sequelize!.fn("DATE", Ticket.sequelize!.col("created_at")),
          "date",
        ],
        [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "count"],
      ],
      group: [
        Ticket.sequelize!.fn("DATE", Ticket.sequelize!.col("created_at")),
      ],
      order: [
        [
          Ticket.sequelize!.fn("DATE", Ticket.sequelize!.col("created_at")),
          "ASC",
        ],
      ],
      raw: true,
    });

    // 2. Status Distribution
    const statusDistribution = await Ticket.findAll({
      where: whereClause,
      attributes: [
        "ticket_status",
        [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "count"],
        [
          Ticket.sequelize!.fn(
            "ROUND",
            Ticket.sequelize!.literal(
              "(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM tickets WHERE " +
                (effectiveOrgId
                  ? `organization_id = '${effectiveOrgId}'`
                  : "1=1") +
                "))"
            )
          ),
          "percentage",
        ],
      ],
      group: ["ticket_status"],
      raw: true,
    });

    // 3. Priority Distribution
    const priorityDistribution = await Ticket.findAll({
      where: whereClause,
      attributes: [
        "priority",
        [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "count"],
      ],
      group: ["priority"],
      raw: true,
    });

    // 4. Module Type Distribution
    const moduleDistribution = await Ticket.findAll({
      where: whereClause,
      attributes: [
        "module_type",
        [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "count"],
      ],
      group: ["module_type"],
      raw: true,
    });

    // 5. Issue Type Distribution
    const issueTypeDistribution = await Ticket.findAll({
      where: whereClause,
      attributes: [
        "issue_type",
        [Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")), "count"],
      ],
      group: ["issue_type"],
      raw: true,
    });

    // 6. Response Time Analytics
    const responseTimeStats = await Ticket.findOne({
      where: {
        ...whereClause,
        first_response_at: { [Op.not]: null },
      },
      attributes: [
        [
          Ticket.sequelize!.fn(
            "AVG",
            Ticket.sequelize!.literal(
              "TIMESTAMPDIFF(HOUR, created_at, first_response_at)"
            )
          ),
          "avg_response_hours",
        ],
        [
          Ticket.sequelize!.fn(
            "MIN",
            Ticket.sequelize!.literal(
              "TIMESTAMPDIFF(HOUR, created_at, first_response_at)"
            )
          ),
          "min_response_hours",
        ],
        [
          Ticket.sequelize!.fn(
            "MAX",
            Ticket.sequelize!.literal(
              "TIMESTAMPDIFF(HOUR, created_at, first_response_at)"
            )
          ),
          "max_response_hours",
        ],
      ],
      raw: true,
    });

    // 7. Resolution Time Analytics
    const resolutionTimeStats = await Ticket.findOne({
      where: {
        ...whereClause,
        resolved_at: { [Op.not]: null },
      },
      attributes: [
        [
          Ticket.sequelize!.fn(
            "AVG",
            Ticket.sequelize!.literal(
              "TIMESTAMPDIFF(HOUR, created_at, resolved_at)"
            )
          ),
          "avg_resolution_hours",
        ],
        [
          Ticket.sequelize!.fn(
            "MIN",
            Ticket.sequelize!.literal(
              "TIMESTAMPDIFF(HOUR, created_at, resolved_at)"
            )
          ),
          "min_resolution_hours",
        ],
        [
          Ticket.sequelize!.fn(
            "MAX",
            Ticket.sequelize!.literal(
              "TIMESTAMPDIFF(HOUR, created_at, resolved_at)"
            )
          ),
          "max_resolution_hours",
        ],
      ],
      raw: true,
    });

    // 8. Agent Performance (if assigned)
    const agentPerformance = await Ticket.findAll({
      where: {
        ...whereClause,
        assigned_to_user_id: { [Op.not]: null },
      },
      attributes: [
        "assigned_to_user_id",
        [
          Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")),
          "total_tickets",
        ],
        [
          Ticket.sequelize!.fn(
            "SUM",
            Ticket.sequelize!.literal(
              "CASE WHEN ticket_status IN ('RESOLVED', 'CLOSED') THEN 1 ELSE 0 END"
            )
          ),
          "resolved_tickets",
        ],
        [
          Ticket.sequelize!.fn(
            "AVG",
            Ticket.sequelize!.literal(
              "CASE WHEN resolved_at IS NOT NULL THEN TIMESTAMPDIFF(HOUR, assigned_at, resolved_at) END"
            )
          ),
          "avg_resolution_time",
        ],
      ],
      group: ["assigned_to_user_id"],
      raw: true,
    });

    // 9. SLA Compliance
    const slaCompliance = await Ticket.findOne({
      where: whereClause,
      attributes: [
        [
          Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("id")),
          "total_tickets",
        ],
        [
          Ticket.sequelize!.fn(
            "SUM",
            Ticket.sequelize!.literal(
              "CASE WHEN sla_due_date IS NULL OR resolved_at <= sla_due_date THEN 1 ELSE 0 END"
            )
          ),
          "sla_met",
        ],
        [
          Ticket.sequelize!.fn(
            "SUM",
            Ticket.sequelize!.literal(
              "CASE WHEN sla_due_date < NOW() AND ticket_status NOT IN ('RESOLVED', 'CLOSED') THEN 1 ELSE 0 END"
            )
          ),
          "overdue_tickets",
        ],
      ],
      raw: true,
    });

    // 10. Customer Satisfaction (if ratings exist)
    const satisfactionStats = await Ticket.findOne({
      where: {
        ...whereClause,
        rating: { [Op.not]: null },
      },
      attributes: [
        [
          Ticket.sequelize!.fn("AVG", Ticket.sequelize!.col("rating")),
          "avg_rating",
        ],
        [
          Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("rating")),
          "total_ratings",
        ],
        [
          Ticket.sequelize!.fn(
            "SUM",
            Ticket.sequelize!.literal("CASE WHEN rating >= 4 THEN 1 ELSE 0 END")
          ),
          "positive_ratings",
        ],
      ],
      raw: true,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("ANALYTICS_FETCHED_SUCCESSFULLY"),
      data: {
        period: {
          type: period,
          date_from: date_from || null,
          date_to: date_to || null,
        },
        overview: {
          total_tickets: totalTickets,
          avg_response_time_hours: responseTimeStats?.avg_response_hours || 0,
          avg_resolution_time_hours:
            resolutionTimeStats?.avg_resolution_hours || 0,
          sla_compliance_rate: slaCompliance
            ? Math.round(
                (slaCompliance.sla_met / slaCompliance.total_tickets) * 100
              )
            : 0,
          customer_satisfaction: satisfactionStats?.avg_rating || 0,
        },
        trends: {
          ticket_volume: ticketTrends,
        },
        distributions: {
          by_status: statusDistribution,
          by_priority: priorityDistribution,
          by_module: moduleDistribution,
          by_issue_type: issueTypeDistribution,
        },
        performance: {
          response_time: responseTimeStats,
          resolution_time: resolutionTimeStats,
          agent_performance: agentPerformance,
          sla_metrics: slaCompliance,
          satisfaction: satisfactionStats,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching analytics:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ANALYTICS_FETCH_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

//  - default export with object
export default {
  getDashboard,
  getTicketsWithFilters,
  bulkTicketOperation,
  getAnalytics,
};
