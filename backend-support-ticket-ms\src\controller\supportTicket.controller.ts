import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { db } from "../models";
import {
  TicketStatus,
  Priority,
  IssueType,
  ModuleType,
} from "../models/Ticket";
import {
  getPaginatedItems,
  getPagination,
  getUser,
  isDefaultAccess,
} from "../utils/common";

// Get models from db object following recipe pattern
const Ticket = db.Ticket;
const TicketAttachment = db.TicketAttachment;

// Helper function for i18n with fallbacks
const getTranslation = (
  res: Response,
  key: string,
  fallback: string
): string => {
  try {
    const translation = res.__(key);
    return translation === key ? fallback : translation;
  } catch (_error) {
    return fallback;
  }
};

/**
 * @description Create a new support ticket
 * @route POST /api/v1/private/tickets
 * @access Private
 */
const createTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      ticket_title,
      ticket_description,
      ticket_module = ModuleType.OTHER,
      ticket_type = IssueType.GENERAL_QUERY,
      ticket_priority = Priority.MEDIUM,
      ticket_owner_name,
      ticket_owner_email,
      ticket_owner_phone,
    } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Generate unique ticket number
    const ticketNumber = `TKT-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;

    // Create ticket
    const newTicket = await Ticket.create({
      ticket_number: ticketNumber,
      ticket_title,
      ticket_description,
      ticket_module,
      ticket_type,
      ticket_priority,
      ticket_status: TicketStatus.OPEN,
      ticket_owner_name,
      ticket_owner_email,
      ticket_owner_phone,
      ticket_owner_user_id: userId,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    });

    // Get created ticket with user details
    const ticketWithDetails = await Ticket.findByPk(newTicket.id, {
      include: [
        {
          model: TicketAttachment,
          as: "attachments",
          required: false,
        },
      ],
    });

    // Fetch user details for response
    const userDetails = await getUser(userId);

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: getTranslation(
        res,
        "TICKET_CREATED_SUCCESSFULLY",
        "Ticket created successfully"
      ),
      data: {
        ...ticketWithDetails?.toJSON(),
        created_by_user: userDetails,
        updated_by_user: userDetails,
      },
    });
  } catch (error: any) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: getTranslation(
        res,
        "ERROR_CREATING_TICKET",
        "Error creating ticket"
      ),
      error: error.message,
    });
  }
};

/**
 * @description Get all support tickets with pagination and filters
 * @route GET /api/v1/private/tickets
 * @access Private
 */
const getAllTickets = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      page,
      limit,
      ticket_status,
      ticket_priority,
      ticket_module,
      ticket_type,
      assigned_to_user_id,
      organization_id,
      search,
      sort_by = "created_at",
      sort_order = "DESC",
    } = req.query;

    const userId = (req as any).user?.id;
    const userOrganizationId = (req as any).user?.organization_id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(userId);

    // Determine effective organization ID following recipe pattern
    let effectiveOrganizationId;
    if (hasDefaultAccess) {
      if (organization_id !== undefined) {
        effectiveOrganizationId =
          organization_id === "null" || organization_id === ""
            ? null
            : organization_id;
      }
    } else {
      effectiveOrganizationId = userOrganizationId;
    }

    // Build where clause
    const whereClause: any = {};

    if (effectiveOrganizationId !== undefined) {
      whereClause.organization_id = effectiveOrganizationId;
    }

    if (ticket_status) {
      whereClause.ticket_status = ticket_status;
    }

    if (ticket_priority) {
      whereClause.ticket_priority = ticket_priority;
    }

    if (ticket_module) {
      whereClause.ticket_module = ticket_module;
    }

    if (ticket_type) {
      whereClause.ticket_type = ticket_type;
    }

    if (assigned_to_user_id) {
      whereClause.assigned_to_user_id = assigned_to_user_id;
    }

    if (search) {
      whereClause[Op.or] = [
        { ticket_number: { [Op.like]: `%${search}%` } },
        { ticket_title: { [Op.like]: `%${search}%` } },
        { ticket_owner_name: { [Op.like]: `%${search}%` } },
        { ticket_owner_email: { [Op.like]: `%${search}%` } },
      ];
    }

    // Query options
    const queryOptions: any = {
      where: whereClause,
      include: [
        {
          model: TicketAttachment,
          as: "attachments",
          required: false,
        },
      ],
      order: [[sort_by as string, sort_order as string]],
    };

    // Apply pagination only if requested
    if (page && limit) {
      const pagination = getPagination(page as string, limit as string);
      queryOptions.limit = pagination.limit;
      queryOptions.offset = pagination.offset;

      const { count, rows } = await Ticket.findAndCountAll(queryOptions);

      // Fetch user details for each ticket
      const ticketsWithUserDetails = await Promise.all(
        rows.map(async (ticket: any) => {
          const ticketData = ticket.toJSON();

          if (ticketData.created_by) {
            ticketData.created_by_user = await getUser(ticketData.created_by);
          }

          if (ticketData.updated_by) {
            ticketData.updated_by_user = await getUser(ticketData.updated_by);
          }

          if (ticketData.assigned_to_user_id) {
            ticketData.assigned_to_user = await getUser(
              ticketData.assigned_to_user_id
            );
          }

          return ticketData;
        })
      );

      const paginatedResult = getPaginatedItems(
        ticketsWithUserDetails,
        count,
        page as string,
        limit as string
      );

      return res.status(StatusCodes.OK).json({
        status: true,
        message: getTranslation(
          res,
          "TICKETS_FETCHED_SUCCESSFULLY",
          "Tickets fetched successfully"
        ),
        ...paginatedResult,
      });
    } else {
      // No pagination
      const tickets = await Ticket.findAll(queryOptions);

      const ticketsWithUserDetails = await Promise.all(
        tickets.map(async (ticket: any) => {
          const ticketData = ticket.toJSON();

          if (ticketData.created_by) {
            ticketData.created_by_user = await getUser(ticketData.created_by);
          }

          if (ticketData.updated_by) {
            ticketData.updated_by_user = await getUser(ticketData.updated_by);
          }

          if (ticketData.assigned_to_user_id) {
            ticketData.assigned_to_user = await getUser(
              ticketData.assigned_to_user_id
            );
          }

          return ticketData;
        })
      );

      return res.status(StatusCodes.OK).json({
        status: true,
        message: getTranslation(
          res,
          "TICKETS_FETCHED_SUCCESSFULLY",
          "Tickets fetched successfully"
        ),
        data: ticketsWithUserDetails,
      });
    }
  } catch (error: any) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: getTranslation(
        res,
        "ERROR_FETCHING_TICKETS",
        "Error fetching tickets"
      ),
      error: error.message,
    });
  }
};
/**
 * @description Update ticket
 * @route PUT /api/v1/private/tickets/:id
 * @access Private
 */
const updateTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { ticket_priority, ticket_status } = req.body;
    const userId = (req as any).user?.id;
    const userOrganizationId = (req as any).user?.organization_id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(userId);

    // Build where clause
    const whereClause: any = { id };

    if (!hasDefaultAccess) {
      whereClause.organization_id = userOrganizationId;
    }

    const ticket = await Ticket.findOne({ where: whereClause });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: getTranslation(res, "TICKET_NOT_FOUND", "Ticket not found"),
      });
    }

    // Update ticket
    const updateData: any = { updated_by: userId };

    if (ticket_priority) {
      updateData.ticket_priority = ticket_priority;
    }

    if (ticket_status) {
      updateData.ticket_status = ticket_status;
    }

    await ticket.update(updateData);

    // Get updated ticket with user details
    const updatedTicket = await Ticket.findByPk(id, {
      include: [
        {
          model: TicketAttachment,
          as: "attachments",
          required: false,
        },
      ],
    });

    // Fetch user details for response
    const ticketData = updatedTicket?.toJSON();

    if (ticketData?.created_by) {
      ticketData.created_by_user = await getUser(ticketData.created_by);
    }

    if (ticketData?.updated_by) {
      ticketData.updated_by_user = await getUser(ticketData.updated_by);
    }

    if (ticketData?.assigned_to_user_id) {
      ticketData.assigned_to_user = await getUser(
        ticketData.assigned_to_user_id
      );
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: getTranslation(
        res,
        "TICKET_UPDATED_SUCCESSFULLY",
        "Ticket updated successfully"
      ),
      data: ticketData,
    });
  } catch (error: any) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: getTranslation(
        res,
        "ERROR_UPDATING_TICKET",
        "Error updating ticket"
      ),
      error: error.message,
    });
  }
};

/**
 * @description Delete ticket
 * @route DELETE /api/v1/private/tickets/:id
 * @access Private
 */
const deleteTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const userOrganizationId = (req as any).user?.organization_id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(userId);

    // Build where clause
    const whereClause: any = { id };

    if (!hasDefaultAccess) {
      whereClause.organization_id = userOrganizationId;
    }

    const ticket = await Ticket.findOne({ where: whereClause });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: getTranslation(res, "TICKET_NOT_FOUND", "Ticket not found"),
      });
    }

    await ticket.destroy();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: getTranslation(
        res,
        "TICKET_DELETED_SUCCESSFULLY",
        "Ticket deleted successfully"
      ),
    });
  } catch (error: any) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: getTranslation(
        res,
        "ERROR_DELETING_TICKET",
        "Error deleting ticket"
      ),
      error: error.message,
    });
  }
};

/**
 * @description Assign ticket to user
 * @route PUT /api/v1/private/tickets/:id/assign
 * @access Private
 */
const assignTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { assigned_to_user_id } = req.body;
    const userId = (req as any).user?.id;
    const userOrganizationId = (req as any).user?.organization_id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(userId);

    // Build where clause
    const whereClause: any = { id };

    if (!hasDefaultAccess) {
      whereClause.organization_id = userOrganizationId;
    }

    const ticket = await Ticket.findOne({ where: whereClause });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: getTranslation(res, "TICKET_NOT_FOUND", "Ticket not found"),
      });
    }

    // Update ticket assignment
    await ticket.update({
      assigned_to_user_id,
      ticket_status: TicketStatus.ASSIGNED,
      updated_by: userId,
    });

    // Get updated ticket with user details
    const updatedTicket = await Ticket.findByPk(id, {
      include: [
        {
          model: TicketAttachment,
          as: "attachments",
          required: false,
        },
      ],
    });

    // Fetch user details for response
    const ticketData = updatedTicket?.toJSON();

    if (ticketData?.created_by) {
      ticketData.created_by_user = await getUser(ticketData.created_by);
    }

    if (ticketData?.updated_by) {
      ticketData.updated_by_user = await getUser(ticketData.updated_by);
    }

    if (ticketData?.assigned_to_user_id) {
      ticketData.assigned_to_user = await getUser(
        ticketData.assigned_to_user_id
      );
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: getTranslation(
        res,
        "TICKET_ASSIGNED_SUCCESSFULLY",
        "Ticket assigned successfully"
      ),
      data: ticketData,
    });
  } catch (error: any) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: getTranslation(
        res,
        "ERROR_ASSIGNING_TICKET",
        "Error assigning ticket"
      ),
      error: error.message,
    });
  }
};

/**
 * @description Resolve ticket
 * @route PUT /api/v1/private/tickets/:id/resolve
 * @access Private
 */
const resolveTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { resolution_note } = req.body;
    const userId = (req as any).user?.id;
    const userOrganizationId = (req as any).user?.organization_id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(userId);

    // Build where clause
    const whereClause: any = { id };

    if (!hasDefaultAccess) {
      whereClause.organization_id = userOrganizationId;
    }

    const ticket = await Ticket.findOne({ where: whereClause });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: getTranslation(res, "TICKET_NOT_FOUND", "Ticket not found"),
      });
    }

    // Update ticket to resolved status
    await ticket.update({
      ticket_status: TicketStatus.RESOLVED,
      resolution_note,
      resolved_at: new Date(),
      resolved_by: userId,
      updated_by: userId,
    });

    // Get updated ticket with user details
    const updatedTicket = await Ticket.findByPk(id, {
      include: [
        {
          model: TicketAttachment,
          as: "attachments",
          required: false,
        },
      ],
    });

    // Fetch user details for response
    const ticketData = updatedTicket?.toJSON();

    if (ticketData?.created_by) {
      ticketData.created_by_user = await getUser(ticketData.created_by);
    }

    if (ticketData?.updated_by) {
      ticketData.updated_by_user = await getUser(ticketData.updated_by);
    }

    if (ticketData?.assigned_to_user_id) {
      ticketData.assigned_to_user = await getUser(
        ticketData.assigned_to_user_id
      );
    }

    if (ticketData?.resolved_by) {
      ticketData.resolved_by_user = await getUser(ticketData.resolved_by);
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: getTranslation(
        res,
        "TICKET_RESOLVED_SUCCESSFULLY",
        "Ticket resolved successfully"
      ),
      data: ticketData,
    });
  } catch (error: any) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: getTranslation(
        res,
        "ERROR_RESOLVING_TICKET",
        "Error resolving ticket"
      ),
      error: error.message,
    });
  }
};

/**
 * @description Rate ticket (customer feedback)
 * @route POST /api/v1/private/tickets/:id/rate
 * @access Private
 */
const rateTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { rating, feedback } = req.body;
    const userId = (req as any).user?.id;
    const userOrganizationId = (req as any).user?.organization_id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(userId);

    // Build where clause
    const whereClause: any = { id };

    if (!hasDefaultAccess) {
      whereClause.organization_id = userOrganizationId;
    }

    const ticket = await Ticket.findOne({ where: whereClause });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: getTranslation(res, "TICKET_NOT_FOUND", "Ticket not found"),
      });
    }

    // Update ticket with rating
    await ticket.update({
      rating,
      feedback,
      rated_at: new Date(),
      rated_by: userId,
      updated_by: userId,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: getTranslation(
        res,
        "TICKET_RATED_SUCCESSFULLY",
        "Ticket rated successfully"
      ),
      data: {
        ticket_id: ticket.id,
        rating,
        feedback,
        rated_at: new Date(),
      },
    });
  } catch (error: any) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: getTranslation(
        res,
        "ERROR_RATING_TICKET",
        "Error rating ticket"
      ),
      error: error.message,
    });
  }
};

// Export all controller functions
export {
  createTicket,
  getAllTickets,
  getTicketById,
  updateTicket,
  deleteTicket,
  assignTicket,
  resolveTicket,
  rateTicket,
};
